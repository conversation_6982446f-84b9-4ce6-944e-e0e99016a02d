"""Webhook服务器核心模块。

此模块以类的方式定义了WebhookServer，不同的实例间相互独立。
提供了完整的Webhook服务器功能，包括：
- FastAPI应用服务
- 认证和授权
- 数据接收和处理
- 定时任务管理
- 多进程支持
"""

import argparse
import asyncio
import functools
import logging
import multiprocessing
import signal
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple
from uuid import uuid4
from zoneinfo import ZoneInfo

import uvicorn
from fastapi import Depends, FastAPI, Header, HTTPException, Query, Request, status
from fastapi.responses import JSONResponse
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from typing_extensions import Annotated

src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent.parent) # noqa

# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
    sys.path.remove(src_path)
sys.path.insert(0,src_path)

from webhook_server.config import constants
from webhook_server.models import server_state,server_properties,server_data
from webhook_server.utils import config_lock
from common.utils import time_utils,network_utils,common_utils,logging_utils,async_utils,self_log,shutdown_exec_funct
from common.models import single_instance_meta


# ------------------ WebhookServer 类封装 ------------------
class WebhookServer(metaclass=single_instance_meta.SingleInstanceMeta):
    def __init__(self,config_path:str=None):
        self.logger: Optional[logging.Logger] = logging.getLogger(__name__) if self_log.log_config.had_init() else None
        logging_utils.logger_print(msg="initializing webhook server instance", custom_logger=self.logger)
        self.web_server_state = server_state.ServerState()
        logging_utils.logger_print(msg="server state created", custom_logger=self.logger)
        if config_path is not None:
            self.set_config_path(config_path)

        # 非子进程需要进行唯一性配置项校验 子进程不校验
        self.is_child_process = False
        self.app = FastAPI()
        logging_utils.logger_print(msg="fastapi app created", custom_logger=self.logger)
        self.security = HTTPBearer()
        logging_utils.logger_print(msg="http bearer security created", custom_logger=self.logger)

        self._create_routes(self.security)
        logging_utils.logger_print(msg="routes created successfully", custom_logger=self.logger)
        logging_utils.logger_print(msg="webhook server instance initialization completed", custom_logger=self.logger)
        shutdown_exec_funct.register(self.cleanup_on_shutdown)

    def set_config_path(self,config_path:str):
        """设置服务端配置文件路径:已经配置的情况下,再次配置则报错"""
        if self.web_server_state.properties is not None:
            raise Exception("config path has been set, can't set again")
        self.web_server_state.properties = server_properties.ServerProperties(config_path, constants.DEFAULT_SCHEDULER_CONFIG)
        if self.logger is None:
            self.logger = logging.getLogger(__name__)
    @staticmethod
    def _had_same_port(host_ports:dict[str,int],port:str):
        """在当前进程使用0.0.0.0时,检测是否有其他进程使用了相同的端口号"""
        for host_port in host_ports.keys():
            if host_port.split('_')[1]==port:
                return True
        return False
    def _check_config_unique_keys(self)->Tuple[bool,str]:
        """"
        检测启动webhook服务端实例的唯一性配置项是否重复 --- 只在非gui模式下使用
        目前校验项:message_data_table_name,port,app_name
        理论上来说，不同进程可以使用不同host加上相同的port来使用,但实际上来说,不需要考虑这种特殊情况,不需要覆盖该场景,直接区分唯一的port即可
        :return: bool True: 唯一, False: 重复(禁止启动)
        """
        config = self.web_server_state.properties.server_config
        port = config["port"]
        config_manager=config_lock.MultiProcessConfigManager.get_singleton_instance(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging=bool(config["enable_sql_logging"]), zone=ZoneInfo(config["time_zone"]))
        return_msg=""

        # 本机对应端口是否被占用 端口唯一性使用
        if network_utils.is_port_occupied(ip='0.0.0.0', port= port):
            return_msg = f"port:{config['port']} has been used by other process, please use port!"
            self.logger.error(return_msg)
            return False,return_msg

        if self.is_child_process:
            # 需要校验配置文件的哈希值和数据库中的是否一致,避免被外部程序修改
            config_manager.check_one_registered_record_valid(server_config_path=self.web_server_state.properties.server_properties_path)
            return True,"child process don't need check config unique keys"
        # 不同进程键相同配置项的值不能相同 DIFF_INSTANCE_UNIQUE_KEYS
        try:
             # 需要清除其中假死和过期的数据
             config_manager._release_validate_cleanup_invalid_records() # noqa
             config_manager.register_config_only_command(file_path=self.web_server_state.properties.server_properties_path)
        except Exception as e:
            return_msg = f"register config error:{e}"
            self.logger.error(return_msg)
            return False,return_msg
        return True,return_msg

    def _create_routes(self,security:HTTPBearer):
        logging_utils.logger_print(msg="creating routes for webhook server", custom_logger=self.logger)
        # 使用闭包解决self引用问题
        async def token_wrapper(request: Request, credentials: Annotated[HTTPAuthorizationCredentials , Depends(security)]):
            api_key = credentials.credentials
            return await self.get_server_token(request, api_key)

        async def save_wrapper(send_data: server_data.SendData, request: Request,x_client_key: Annotated[Optional[str], Header()] = None):
            return await self.save_message(send_data, request, x_client_key)

        async def unread_wrapper(request: Request,size: Annotated[int, Query(gt=0)],credentials: Annotated[HTTPAuthorizationCredentials , Depends(security)],client_key: Annotated[str|None, Query()] = None,minutes: Annotated[int|None, Query()] = None):
            token = credentials.credentials
            return await self.get_client_unread(request, size, token, client_key,minutes)

        logging_utils.logger_print(msg="registering api routes", custom_logger=self.logger)
        # 注册路由
        self.app.get("/webhook/token")(token_wrapper)
        logging_utils.logger_print(msg="registered get /webhook/token route", custom_logger=self.logger)
        self.app.post("/webhook/save")(save_wrapper)
        logging_utils.logger_print(msg="registered post /webhook/save route", custom_logger=self.logger)
        self.app.get("/webhook/unread")(unread_wrapper)
        logging_utils.logger_print(msg="registered get /webhook/unread route", custom_logger=self.logger)

        logging_utils.logger_print(msg="registering middleware", custom_logger=self.logger)
        # 注册中间件
        self.app.middleware("http")(self.check_ip_whitelist_log_requests)
        logging_utils.logger_print(msg="registered ip whitelist middleware", custom_logger=self.logger)
        logging_utils.logger_print(msg="routes creation completed", custom_logger=self.logger)


    # 检查IP白名单 --- request 请求log
    async def check_ip_whitelist_log_requests(self, request: Request, call_next):
        if not request.client or not request.client.host:
            self.logger.error("invalid client connection")
            return JSONResponse({"error": "invalid client"}, status_code=400)
            
        client_ip = request.client.host
        whitelist = self.web_server_state.properties.server_config["whitelist"]
        self.logger.debug(f"checking ip whitelist for client: {client_ip}, path: {request.url.path}")
        self.logger.debug(f"current whitelist: {whitelist}")
        
        if (whitelist[0] != "*" and request.url.path in constants.IP_CHECK_API_LIST
                and not network_utils.is_ip_in_whitelist(client_ip, whitelist)):
            self.logger.error(f"this ip:{client_ip} not allow request!")
            return JSONResponse({"error": "IP not allowed"}, status_code=403)
            
        start_time = time.time()
        self.logger.debug(f"processing request from {client_ip} to {request.url.path}")
        try:
            response = await call_next(request)
        except Exception as e:
            return JSONResponse({"error": str(e)}, status_code=500)
        process_time = (time.time() - start_time) * 1000
        self.logger.info(f"{client_ip} - {request.method} {request.url.path} - {response.status_code} - {process_time:.2f}ms")
        return response

    # 根据api_key获取当前有效的token
    async def get_server_token(self, request: Request, api_key: str):
        self.logger.debug(f"token request received from {request.client.host}, validating api_key")
        if api_key != self.web_server_state.properties.server_config["api_key"]:
            self.logger.error(f"this request api key invalid from {request.client.host}!")
            raise HTTPException(status.HTTP_403_FORBIDDEN)
        self.logger.info(f"{request.client.host} get server token")
        return {"token": self.web_server_state.server_refresh_token}

    # 接收发信方消息
    async def save_message(self, send_data: server_data.SendData, request: Request,
                           x_client_key: Annotated[Optional[str], Header()] = None):
        self.logger.debug(f"save message request received from {request.client.host}, client_key: {x_client_key}")
        client_key_to_desc = self.web_server_state.properties.client_info_properties
        # 发信方标识大小写敏感
        x_client_key = common_utils.trim(x_client_key)

        if not x_client_key or x_client_key not in client_key_to_desc:
            self.logger.error(f"invalid client key:{x_client_key} from {request.client.host}!")
            raise HTTPException(status.HTTP_403_FORBIDDEN, "Invalid client key")

        self.logger.info(f"receive message from {client_key_to_desc[x_client_key]}")
        self.logger.debug(f"message content length: {len(send_data.content) if send_data.content else 0}")
        message_id = self.web_server_state.properties.data_manager.save_message(send_data.content, x_client_key)
        self.logger.debug(f"message saved with id: {message_id}")
        return {"status": "success", "message_id": message_id}

    # 读取指定发信方消息:client_key为空则获取所有未读消息
    async def get_client_unread(self, request: Request, size: int, token: str, client_key: Optional[str] = None,minutes:Optional[int]=None):
        self.logger.debug(f"get unread messages request from {request.client.host}, size: {size}, client_key: {client_key}")

        if token != self.web_server_state.server_refresh_token:
            self.logger.error(f"unauthorized client {request.client.host} read messages")
            raise HTTPException(status.HTTP_401_UNAUTHORIZED)

        client_key_to_desc = self.web_server_state.properties.client_info_properties
        client_key = common_utils.trim(client_key)

        if client_key is not None and client_key not in client_key_to_desc:
                self.logger.error(f"invalid client key:{client_key} from {request.client.host}!")
                # 统一接口
                return {"messages": []}
        if minutes is None:
            minutes=5
        # 读取最近 minutes 分钟未读消息
        self.logger.debug(f"retrieving up to {size} unread messages with retry limit {minutes}")
        msgs = self.web_server_state.properties.data_manager.get_recent_unread(size=size,minutes= minutes, client_key=client_key)
        self.logger.info(f"read unread message: {len(msgs)}")
        return {"messages": msgs}

    def scheduler_running(self):
        return hasattr(self, 'web_server_state') and hasattr(self.web_server_state, 'properties') and hasattr(self.web_server_state.properties, 'scheduler') and self.web_server_state.properties.scheduler.running and not self.web_server_state.end_flag.is_set()

    # 定时刷新token
    def refresh_token(self):
        self.web_server_state.server_refresh_token = str(uuid4())
        logging_utils.logger_print("server token refreshed!", custom_logger=self.logger)

    # 如果存在过期[3]天未读的数据则发出警告消息
    def expired_unread_data_warning(self):
        expired_days = 3
        expired_unread_data_size = self.web_server_state.properties.data_manager.get_older_unread_count(expired_days)
        if expired_unread_data_size > 0:
            logging_utils.logger_print(f"there are {expired_unread_data_size} unread data that have expired for more than {expired_days} days, please handle it.", custom_logger=self.logger, log_level=logging.ERROR)

    # 本程序中在结束时需要做的事情
    async def cleanup_on_shutdown(self):
        self._end_do()

    # 该实例销毁时执行,只能被执行一次
    def _end_do(self):
        common_utils.run_once(self.__end_do_once)
    def __end_do_once(self):
        logging_utils.logger_print("executing __end_do_once function", custom_logger=self.logger, log_level=logging.ERROR)
        web_server_state_exist = hasattr(self, 'web_server_state') and self.web_server_state is not None
        if web_server_state_exist:
            logging_utils.logger_print("resetting and clearing web_server_state", custom_logger=self.logger, log_level=logging.ERROR)
            common_utils.run_once(self.web_server_state.reset_clear)

        # 清理其他资源
        self.app = None
        self.security = None
    def __del__(self):
        self._end_do()

    # 所有的定时任务都在这里添加
    def add_tasks(self,zone_info:ZoneInfo,config:dict,always_run:bool,end_time:time):
        task1=functools.partial(async_utils.run_safe_task,task_func=self.refresh_token,scheduler_check= self.scheduler_running, task_canceled_info="refresh_token_job cancelled(normal during shutdown)...",task_exception_info= "refresh_token_job occurred exception!",custom_logger= self.logger)
        task2=functools.partial(async_utils.run_safe_task,task_func=self.expired_unread_data_warning,scheduler_check= self.scheduler_running, task_canceled_info="expired_unread_data_warning_job cancelled(normal during shutdown)...",task_exception_info= "expired_unread_data_warning_job occurred exception!",custom_logger= self.logger)
        task3 = functools.partial(async_utils.run_safe_task, task_func=self.web_server_state.properties.data_manager.remove_excess_read_data, scheduler_check=self.scheduler_running, task_canceled_info="remove_excess_read_data_job cancelled(normal during shutdown)...", task_exception_info="remove_excess_read_data_job occurred exception!", custom_logger=self.logger,data_limit_num=config["data_limit_num"])
        task4=functools.partial(async_utils.run_safe_task,task_func=self.web_server_state.properties.data_manager.remove_expired_read_data,scheduler_check= self.scheduler_running, task_canceled_info="remove_expired_read_data_job cancelled(normal during shutdown)...",task_exception_info= "remove_expired_read_data_job occurred exception!",custom_logger= self.logger,expire_data_days=config["expire_data_days"])
        task7=functools.partial(async_utils.run_safe_task,task_func=self.cleanup_on_shutdown,scheduler_check= self.scheduler_running, task_canceled_info="cleanup_on_shutdown cancelled(normal during shutdown)...",task_exception_info= "cleanup_on_shutdown occurred exception!",custom_logger= self.logger)

        self.web_server_state.properties.scheduler.add_job(task1, 'interval', minutes=5, next_run_time=datetime.now(zone_info),id="refresh_token_job")
        self.web_server_state.properties.scheduler.add_job(task2, 'interval', hours=1, next_run_time=datetime.now(zone_info),id="expired_unread_data_warning_job")
        self.web_server_state.properties.scheduler.add_job(task3,'interval', minutes=1, id="remove_excess_read_data_job")
        self.web_server_state.properties.scheduler.add_job(task4,'interval', minutes=1, id="remove_expired_read_data_job")
        # 补充关闭时触发的定时任务
        if not always_run:
            self.web_server_state.properties.scheduler.add_job(task7, trigger='cron', hour=end_time.hour, minute=end_time.minute, id="cleanup_on_shutdown_job")



    # 启动uvicorn服务
    async def run_server(self):
        config = self.web_server_state.properties.server_config
        zone_info = self.web_server_state.properties.server_config_zone

        try:
            can_run, always_run, start_time, end_time, now = time_utils.check_runtime_allowed(config["run_time"], zone_info)
            if can_run:
                check_valid,error_msg = self._check_config_unique_keys()
                if not check_valid:
                    raise ValueError(error_msg)
                self.web_server_state.properties.scheduler.start()

                self.add_tasks(zone_info,config,always_run,end_time)
                self.web_server_state.webhook_server = uvicorn.Server(uvicorn.Config(
                    self.app, host=config["host"], port=config["port"],backlog=2048,
                    log_config=None, log_level=None, access_log=False
                ))
                await self.web_server_state.webhook_server.serve()

        except asyncio.CancelledError:
            logging_utils.logger_print("webhook server cancelled, shutting down...", custom_logger=self.logger, use_exception=True)
        finally:
            await self.cleanup_on_shutdown()

# ------------------ 服务管理 ------------------
def _get_init_webhook_server_(config_path: str,is_child_process=False)->WebhookServer:
    """
    根据配置文件初始化webhook server实例,给后续的run_server使用
    :param config_path:
    :return: WebhookServer实例
    """
    try:
        server = WebhookServer(config_path=config_path)
        server.is_child_process = is_child_process
        return server
    except (ValueError, FileNotFoundError, PermissionError) as config_error:
        logging_utils.logger_print(f"configuration error during server initialization: {config_error}", custom_logger= None, use_exception=True, exception=config_error)
        raise ValueError(f"配置文件配置项不合规范: {config_error}")
    except Exception as e:
        logging_utils.logger_print(f"unexpected error during server initialization: {e}", custom_logger= None, use_exception=True, exception=e)
        raise RuntimeError(f"服务器初始化失败: {e}")
    except BaseException as e:
        logging_utils.logger_print(f"critical error during server initialization: {e}", custom_logger= None, exception=e)
        raise

# 根据配置文件启动web服务
async def run_server_with_path(config_path: str,is_child_process=False):
    server = _get_init_webhook_server_(config_path,is_child_process)  # 每次调用创建新的实例
    try:
        logging_utils.logger_print("starting webhook server", custom_logger= server.logger)
        await server.run_server()
    except KeyboardInterrupt:
        # 单独处理Ctrl+C
        logging_utils.logger_print("server stopped by user request", custom_logger= server.logger)
        sys.exit(0)
    except asyncio.CancelledError:
        logging_utils.logger_print("server cancelled during startup", custom_logger=  server.logger)
        sys.exit(0)
    except (ValueError, RuntimeError) as expected_error:
        logging_utils.logger_print(f"expected error during server execution: {expected_error}", custom_logger= server.logger, use_exception=True)
        sys.exit(1)
    except Exception as e:
        logging_utils.logger_print(f"unexpected error during server execution: {e}", custom_logger= server.logger, use_exception=True)
        sys.exit(1)
    except BaseException as e:
        logging_utils.logger_print(f"critical error during server execution: {e}", custom_logger= server.logger, use_exception=True)
        sys.exit(2)
    finally:
        if server and hasattr(server, 'web_server_state') and hasattr(server.web_server_state, 'end_flag'):
            server.web_server_state.end_flag.set()

async def _wait_for_shutdown(server:WebhookServer,shutdown_event:multiprocessing.Event):
    """
    等待子进程关闭事件
    :param shutdown_event: 关闭事件
    :return:
    """
    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, shutdown_event.wait) # noqa

    # 当收到关闭信号时，执行清理操作
    logging_utils.logger_print(msg="shutdown event detected, running cleanup...", custom_logger=server.logger)
    await server.cleanup_on_shutdown()
    await asyncio.sleep(0.2)
    loop.stop()

def _async_run_server_in_child_process(config_path: str,shutdown_event:multiprocessing.Event):
    """
    带有停止事件的子进程启动函数
    :param config_path: 配置文件路径
    :param shutdown_event: 停止事件 父进程可以控制子进程的平顺停止
    """
    server = _get_init_webhook_server_(config_path,is_child_process=True)
    # 设置子进程忽略键盘中断 (Ctrl+C)
    signal.signal(signal.SIGINT, signal.SIG_IGN)

    # 创建新的事件循环
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        # 创建异步任务
        main_task = loop.create_task(server.run_server())

        # 添加关闭事件检查任务
        shutdown_check_task = loop.create_task(_wait_for_shutdown(server,shutdown_event))

        # 同时运行主任务和关闭监控
        done, pending = loop.run_until_complete(asyncio.wait(
            [main_task, shutdown_check_task],return_when=asyncio.FIRST_COMPLETED
        ))


        # 执行最终清理
        for task in pending:
            task.cancel()

        logging_utils.logger_print(msg="All tasks cleaned up", custom_logger=server.logger)
        logging_utils.logger_print(msg="[Child] server stopped", custom_logger= server.logger)
    except asyncio.TimeoutError:
        logging_utils.logger_print(msg="[Child] Shutdown timed out", custom_logger=server.logger, log_level=logging.WARNING)
    except Exception as e:
        logging_utils.logger_print(msg="[Child] Error in async process!", custom_logger= server.logger, use_exception=True, exception=e)
    finally:
        # 确保在关闭事件循环前执行清理操作
        try:
            if not loop.is_closed():
                cleanup_task = asyncio.ensure_future(server.cleanup_on_shutdown(), loop=loop)
                loop.run_until_complete(cleanup_task)
            tasks = [t for t in asyncio.all_tasks(loop) if not t.done()]
            for task in tasks:
                task.cancel()
            if tasks:
                loop.run_until_complete(asyncio.gather(*tasks, return_exceptions=True))

            if not loop.is_closed():
                loop.run_until_complete(loop.shutdown_asyncgens())
                loop.run_until_complete(loop.shutdown_default_executor())
                loop.close()
        except Exception as e:
            logging_utils.logger_print(msg="[Child] Final cleanup error", custom_logger=server.logger, use_exception=True, exception=e)
        finally:
            logging_utils.logger_print(msg="[Child] Process exiting", custom_logger=server.logger)

def get_child_process(config_path: str,shutdown_event:multiprocessing.Event)->multiprocessing.Process:
    """
    根据配置文件路径和关闭事件创建子进程,真正进行子进程的启动需要调用方实现
    :param config_path:  配置文件路径
    :param shutdown_event:  关闭事件 调用方决定该子进程的停止时机
    :return: 子进程实例
    """
    return multiprocessing.Process(target=_async_run_server_in_child_process, args=(config_path,shutdown_event),daemon=True)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    asyncio.run(run_server_with_path(parser.parse_args().config))
