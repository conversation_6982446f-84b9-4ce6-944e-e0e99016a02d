# 代码功能测试
import configparser
import logging
import math
import os
import sys
from datetime import datetime, time
from typing import Optional
from zoneinfo import ZoneInfo

import pytest

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

from webhook_server.config import constants, gui_constants
from webhook_server.models import server_properties
from common.utils import self_log,time_utils,common_utils,logging_utils,config_utils

logger: Optional[logging.Logger] = None


def echo_diff_zone_date_time():
    z1 = ZoneInfo('UTC')
    z2 = ZoneInfo('Asia/Shanghai')
    datetime1 = datetime(2023, 1, 1, 10, 0, 0, tzinfo=z1)
    logger.info(f"2023-01-01 10:00:00 UTC: {datetime1}")
    logger.info(
        f"datetime1 date: {datetime1.date()}, datetime1 time: {datetime1.time()}, datetime1 tz: {datetime1.tzinfo}")
    datetime2 = datetime(2023, 1, 1, 10, 0, 0, tzinfo=z2)
    logger.info(f"2023-01-01 10:00:00 Shanghai: {datetime2}")
    logger.info(
        f"datetime2 date: {datetime2.date()}, datetime2 time: {datetime2.time()}, datetime2 tz: {datetime2.tzinfo}")
    combine1 = datetime.combine(datetime1, datetime2.time(), tzinfo=z2)
    logger.info(f"combine1: {combine1}")
    logger.info(f"combine1 date: {combine1.date()}, combine1 time: {combine1.time()}, combine1 tz: {combine1.tzinfo}")
    combine2 = datetime.combine(datetime2, datetime1.time(), tzinfo=z1)
    logger.info(f"combine2: {combine2}")
    logger.info(f"combine2 date: {combine2.date()}, combine2 time: {combine2.time()}, combine2 tz: {combine2.tzinfo}")
    t1 = time(12, 0, 0, tzinfo=z1)
    logger.info(f"12:00:00 UTC: {t1}")
    t2 = time(12, 0, 0, tzinfo=z2)
    logger.info(f"12:00:00 Shanghai: {t2}")
    combine3 = datetime.combine(datetime1.date(), t2, tzinfo=datetime1.tzinfo)
    logger.info(f"combine3: {combine3}")


def get_next_datetime_same_day_test():
    """测试当天就能满足条件的情况"""
    start_time = datetime(2023, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("UTC"))
    target_time = time(12, 0, 0)
    result = time_utils.get_next_datetime(start_time, target_time)
    expected = datetime(2023, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC"))
    assert result == expected


def get_next_datetime_next_day_test():
    """测试需要到第二天才能满足条件的情况"""
    start_time = datetime(2023, 1, 1, 14, 0, 0, tzinfo=ZoneInfo("UTC"))
    target_time = time(12, 0, 0)
    result = time_utils.get_next_datetime(start_time, target_time)
    expected = datetime(2023, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("UTC"))
    assert result == expected


def get_next_datetime_edge_case_test():
    """测试边界情况：开始时间正好等于目标时间"""
    start_time = datetime(2023, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("UTC"))
    target_time = time(12, 0, 0)
    result = time_utils.get_next_datetime(start_time, target_time)
    expected = datetime(2023, 1, 2, 12, 0, 0, tzinfo=ZoneInfo("UTC"))
    assert result == expected


def get_next_datetime_different_timezone_test():
    """测试不同时区的情况"""
    start_time = datetime(2023, 1, 1, 10, 0, 0, tzinfo=ZoneInfo("America/New_York"))
    target_time = time(12, 0, 0)
    result = time_utils.get_next_datetime(start_time, target_time)
    expected = datetime(2023, 1, 1, 12, 0, 0, tzinfo=ZoneInfo("America/New_York"))
    assert result == expected


def get_next_datetime_midnight_test():
    """测试午夜边界情况"""
    start_time = datetime(2023, 1, 1, 23, 59, 59, tzinfo=ZoneInfo("UTC"))
    target_time = time(0, 0, 0)
    result = time_utils.get_next_datetime(start_time, target_time)
    expected = datetime(2023, 1, 2, 0, 0, 0, tzinfo=ZoneInfo("UTC"))
    assert result == expected


# -------------
def earlier_same_day_test():
    """TC001: 当前时间早于目标时间（返回当天）"""
    start = datetime(2023, 1, 1, 12, 0)
    target = time(14, 0)
    expected = datetime(2023, 1, 1, 14, 0)
    assert time_utils.get_next_datetime(start, target) == expected


def later_next_day_test():
    """TC002: 当前时间晚于目标时间（返回次日）"""
    start = datetime(2023, 1, 1, 15, 0)
    target = time(14, 0)
    expected = datetime(2023, 1, 2, 14, 0)
    assert time_utils.get_next_datetime(start, target) == expected


def month_crossover_test():
    """TC004: 跨月场景验证（1月31日→2月1日）"""
    start = datetime(2023, 1, 31, 15, 0)
    target = time(14, 0)
    expected = datetime(2023, 2, 1, 14, 0)
    assert time_utils.get_next_datetime(start, target) == expected


def timezone_awareness_test():
    """TC005: 时区敏感场景验证"""
    tz = ZoneInfo('Asia/Shanghai')
    start = datetime(2023, 1, 1, 15, 0, tzinfo=tz)
    target = time(14, 0)
    result = time_utils.get_next_datetime(start, target)
    assert result.tzinfo == start.tzinfo
    assert result == datetime(2023, 1, 2, 14, 0, tzinfo=tz)


def all_tests_get_next_datetime():
    get_next_datetime_same_day_test()
    get_next_datetime_next_day_test()
    get_next_datetime_edge_case_test()
    get_next_datetime_different_timezone_test()
    get_next_datetime_midnight_test()
    earlier_same_day_test()
    later_next_day_test()
    month_crossover_test()
    timezone_awareness_test()

async def read_server_properties():
    cur_server_properties = server_properties.ServerProperties(server_properties_path="../resources/server_config.ini")
    logger.info("server_properties_path: %s", cur_server_properties.server_properties_path)
    logger.info("[server] key-value: %s", cur_server_properties.server_config)
    logger.info("[client_info] keys: %s", cur_server_properties.client_info_properties.keys())
    logger.info("data_manager not None: %s", cur_server_properties.data_manager is not None)
    logger.info("[server.time_zone] value: %s", cur_server_properties.server_config["time_zone"])


def convert_storage_str_to_bytes_valid():
    # logger.info(f"容量正则表达式:{constants.STORAGE_UNIT_PATTERN}")
    # logger.info(f"正则表达式匹配结果:{constants.STORAGE_UNIT_PATTERN.match('100MB').group(1)}")
    # 基本单位测试
    assert math.isclose(common_utils.convert_storage_str_to_bytes("500B"), 500.0)
    assert math.isclose(common_utils.convert_storage_str_to_bytes("1KB"), 1024.0)
    assert math.isclose(common_utils.convert_storage_str_to_bytes("2.5MB"), 2.5 * 1024 ** 2)
    assert math.isclose(common_utils.convert_storage_str_to_bytes("3.35GB"), 3.35 * 1024 ** 3)
    assert math.isclose(common_utils.convert_storage_str_to_bytes("0.1TB"), 0.1 * 1024 ** 4)

    # 边界值测试
    assert math.isclose(common_utils.convert_storage_str_to_bytes("0B"), 0.0)
    assert math.isclose(common_utils.convert_storage_str_to_bytes("1023.999MB"), 1023.999 * 1024 ** 2)

    # 带空格和大小写测试
    assert math.isclose(common_utils.convert_storage_str_to_bytes(" 1.2MB "), 1.2 * 1024 ** 2)
    assert math.isclose(common_utils.convert_storage_str_to_bytes("2gb"), 2 * 1024 ** 3)
    assert math.isclose(common_utils.convert_storage_str_to_bytes(" 3.5 Tb"), 3.5 * 1024 ** 4)
    assert math.isclose(common_utils.convert_storage_str_to_bytes("4.5 tB "), 4.5 * 1024 ** 4)


def convert_storage_str_to_bytes_invalid():
    # 无效单位测试
    with pytest.raises(ValueError) as e:
        common_utils.convert_storage_str_to_bytes("1.2AB")
    assert "unknown unit: 'AB'" in str(e.value)

    # 无效格式测试
    with pytest.raises(ValueError):
        common_utils.convert_storage_str_to_bytes("12X34MB")

    # 空值测试
    with pytest.raises(ValueError):
        common_utils.convert_storage_str_to_bytes("")

    # 纯空格测试
    with pytest.raises(ValueError):
        common_utils.convert_storage_str_to_bytes("   ")


def trim_test():
    test_cases = [
        # (输入值, 期望输出, 测试描述)
        ("  hello  ", "hello", "前后存在空格"),
        ("\tvalue\n", "value", "包含制表符和换行符"),
        ("  ", None, "全空格字符串"),
        ("", None, "空字符串"),
        (None, None, "输入为None"),
        ("no_trim", "no_trim", "无需修剪的字符串"),
        (123, None, "数字类型输入"),
        (True, None, "布尔类型输入"),
        ("  a b  ", "a b", "中间保留空格"),
        ("  保留中文空格  ", "保留中文空格", "中文字符处理"),
        ("  ", None, "全角空格测试")  # 假设全角空格会被strip()处理
    ]

    passed = 0
    failed = 0

    for input_val, expected, description in test_cases:
        try:
            result = common_utils.trim(input_val)
            assert result == expected
            passed += 1
            print(f"✅ [{description}] 通过")
        except Exception as e:
            failed += 1
            print(f"❌ [{description}] 失败")
            print(f"   输入: {repr(input_val)}")
            print(f"   预期: {repr(expected)}")
            print(f"   实际: {repr(e)}")

    print(f"\n测试结果：{passed} 通过，{failed} 失败")

def convert_storage_str_to_bytes_test():
    convert_storage_str_to_bytes_valid()
    convert_storage_str_to_bytes_invalid()


# 小的测试
def other_test():
    try:
        print(f"{constants.SERVER_RUN_TIME_PATTERN.match(None)}")
    except Exception as e:
        print(f"exception occurred：{e}")


def except_log_test():
    try:
        1 / 0
    except:
        logger.exception("出现异常!")


def dict_none_test():
    d = {"a": 1, "b": None, "c": 3}
    print(d)
    # 键不存在设置默认值,当前b这个键是存在的，所以返回None
    print(d.get("b", "default_value_b"))
    print(d.get("d", "default_value_d"))
    print(d.get("e"))


def read_log_config_test():
    config_path = "./other_conf/empty_log.ini"
    config = configparser.ConfigParser(interpolation=None)
    config.read(config_path, encoding="utf-8")
    log_config_section = self_log.LogConfig.get_log_config(config)
    print(log_config_section)
    console_config = log_config_section["console"]
    print(f"type:{type(console_config)} , value:{console_config}")

def set_log_once_test():
    global logger
    self_log.setup_logging("../resources/log.ini")
    self_log.setup_logging("./other_conf/empty_log.ini")
    logger=logging.getLogger(__name__)
    logger.debug("test log once")

def print_tmp_file_test():
    msg_infos= [None,"","中文","England","传递cdv","的_6^……$￥#@！~{}‘；。，、’；‘：’;'';/.,;'[]"]
    separator_line="-" * 50
    for msg_info in msg_infos:
        logging_utils.print_tmp_file(msg_info)
    logging_utils.print_tmp_file(separator_line)
    for msg_info in msg_infos:
        logging_utils.print_tmp_file(msg_info, print_error=True)
    logging_utils.print_tmp_file(separator_line, print_error=True)
    ex=Exception("test exception")
    for msg_info in msg_infos:
        logging_utils.print_tmp_file(msg_info, print_error=False, exception=ex)
    logging_utils.print_tmp_file(separator_line)

def save_selection_to_file_test():
    server_section={"1":"x","3":"y"}
    config_utils.update_config(gui_constants.SERVER_CONFIG_PATH, "server", server_section)

if __name__ == '__main__':
    # read_server_properties()
    # echo_diff_zone_date_time()
    # all_tests_get_next_datetime()
    convert_storage_str_to_bytes_test()
    # trim_test()
    # other_test()
    # except_log_test()
    # dict_none_test()
    # read_log_config_test()
    # set_log_once_test()
    # print_tmp_file_test()
    # save_selection_to_file_test()
    # print(f"cur machine lan ip:{get_local_lan_ip()}")
